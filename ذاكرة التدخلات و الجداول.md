# ذاكرة التدخلات والجداول

هذا الملف يحتوي على ذاكرة جميع العمليات المتعلقة بنظام التدخلات والجداول.

## 📋 قائمة المهام الرئيسية

### 1️⃣ إنشاء واجهة التدخلات اليومية المتقدمة الرئيسية
**الحالة:** [ ] لم تبدأ
**الوصف:** إنشاء الصفحة الرئيسية للتدخلات اليومية المتقدمة على http://127.0.0.1:8000/coordination-center/daily-interventions/ مع الأزرار الأربعة الرئيسية (بلاغ أولي، عملية التعرف، إنهاء المهمة، صفحة الجداول) والجدول الرئيسي لعرض التدخلات

### 2️⃣ تطوير نموذج البلاغ الأولي
**الحالة:** [ ] لم تبدأ
**الوصف:** إنشاء نموذج البلاغ الأولي مع جميع الحقول المطلوبة (ساعة الخروج، مكان التدخل، نوع التدخل، الوسيلة المرسلة، الجهة المتصلة، نوع الاتصال، رقم الهاتف، ملاحظات) مع ربطه بقاعدة البيانات

### 3️⃣ تطوير نماذج عملية التعرف لجميع أنواع التدخلات
**الحالة:** [ ] لم تبدأ
**الوصف:** إنشاء نماذج عملية التعرف المخصصة لكل نوع تدخل (الإجلاء الصحي، حوادث المرور، حريق المحاصيل الزراعية، حرائق البنايات والمؤسسات، العمليات المختلفة) مع جميع الحقول المحددة لكل نوع

### 4️⃣ تطوير نماذج إنهاء المهمة لجميع أنواع التدخلات
**الحالة:** [ ] لم تبدأ
**الوصف:** إنشاء نماذج إنهاء المهمة المخصصة لكل نوع تدخل مع حقول الإحصائيات والنتائج النهائية (المسعفين، الوفيات، الخسائر، الملاحظات الختامية) وحساب مدة التدخل تلقائياً

### 5️⃣ تطوير نظام طلب الدعم والتصعيد
**الحالة:** [ ] لم تبدأ
**الوصف:** إنشاء آلية طلب الدعم من الوحدات المجاورة ومركز التنسيق الولائي، مع نظام التصعيد إلى الكوارث الكبرى وإرسال الإنذارات الصوتية

### 6️⃣ إنشاء صفحة الجداول المتقدمة
**الحالة:** [ ] لم تبدأ
**الوصف:** إنشاء صفحة http://127.0.0.1:8000/coordination-center/all-interventions/ مع جداول منفصلة لكل نوع تدخل (الإجلاء الصحي، حوادث المرور، حريق المحاصيل الزراعية، حرائق البنايات والمؤسسات، العمليات المختلفة) مع أزرار للمراحل الثلاث

### 7️⃣ تطوير نظام الصلاحيات والفلترة
**الحالة:** [ ] لم تبدأ
**الوصف:** إنشاء نظام صلاحيات حيث مدير الولاية يختار الوحدة المراد رؤيتها والأدمن يختار الولاية والوحدة، مع فلترة البيانات لعرض 24 ساعة وإمكانية الفلترة حسب التاريخ والساعة

### 8️⃣ إصلاح جدول حريق المحاصيل الزراعية
**الحالة:** [ ] لم تبدأ
**الوصف:** حذف عمود 'الحالة (سائق/راكب/مشاة)' من جدول حريق المحاصيل الزراعية وإصلاح عرض البيانات الفارغة في صفحة تفاصيل التدخل

### 9️⃣ إنشاء صفحة الكوارث الكبرى
**الحالة:** [ ] لم تبدأ
**الوصف:** إنشاء صفحة http://127.0.0.1:8000/major-disasters/ للكوارث الكبرى مع واجهة مركز التنسيق الولائي والوطني، أو عرض رسالة 'سيتم تطويرها قريباً' إذا لم تكن موجودة

### 🔟 تطوير واجهة مركز التنسيق الولائي للكوارث الكبرى
**الحالة:** [ ] لم تبدأ
**الوصف:** إنشاء واجهة مركز التنسيق الولائي للكوارث الكبرى مع عرض البلاغات المصنفة كـ 'كارثة كبرى'، خريطة تفاعلية، نظام تصعيد للمركز الوطني، وتوزيع الدعم من الوحدات التابعة

### 1️⃣1️⃣ تطوير واجهة مركز التنسيق الوطني للكوارث الكبرى
**الحالة:** [ ] لم تبدأ
**الوصف:** إنشاء واجهة مركز التنسيق الوطني للكوارث الكبرى مع استلام البلاغات المصعدة، تنسيق الدعم بين الولايات، خريطة تفاعلية وطنية، ولوحة مؤشرات حية

### 1️⃣2️⃣ ربط النظام مع قاعدة بيانات الوسائل والأطقم
**الحالة:** [ ] لم تبدأ
**الوصف:** ربط نظام التدخلات مع صفحة http://127.0.0.1:8000/vehicle-crew-assignment/ لاستيراد الوسائل المتاحة وتسجيل الوسائل المرسلة في التدخلات

### 1️⃣3️⃣ تطوير نظام التحديث التلقائي للحالات
**الحالة:** [ ] لم تبدأ
**الوصف:** إنشاء نظام تحديث تلقائي لحالات التدخلات (قيد التعرف، عملية تدخل، منتهية، كارثة كبرى) مع ظهور الأزرار المناسبة في عمود الإجراءات

### 1️⃣4️⃣ تطوير نظام الإشعارات والتنبيهات
**الحالة:** [ ] لم تبدأ
**الوصف:** إنشاء نظام إشعارات صوتية عند طلب الدعم والتصعيد، مع إرسال التنبيهات لمراكز التنسيق المختلفة حسب نوع الطلب

### 1️⃣5️⃣ اختبار وتحسين النظام
**الحالة:** [ ] لم تبدأ
**الوصف:** إجراء اختبارات شاملة للنظام، التأكد من عمل جميع الوظائف، إصلاح الأخطاء، وتحسين الأداء والواجهة

---

## 📝 سجل التقدم

**تاريخ البدء:** 1 أغسطس 2025
**آخر تحديث:** 1 أغسطس 2025

### المهام المكتملة:
- لا توجد مهام مكتملة بعد

### المهام قيد التنفيذ:
- لا توجد مهام قيد التنفيذ حالياً

### الملاحظات:
- تم إنشاء قائمة المهام الأساسية بناءً على المتطلبات المحددة في الملفات المرجعية
- يجب البدء بالمهمة الأولى (إنشاء الواجهة الرئيسية) كأساس للنظام
- كل مهمة مكتملة ستُحدث حالتها إلى [x] منتهية
